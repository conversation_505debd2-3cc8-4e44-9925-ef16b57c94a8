//- Thằng layouts này dùng bố cục chung cho nhiều trang web
doctype html
html 
    head 
        link(rel="stylesheet", href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css")
        link(rel="stylesheet", href="/css/style.css")
        //- truyền data động từ home.controller
        title #{pageTitle}
        

    body 
        //- chia header với footer ra để sau này code nhiều cho dễ 
        include ../partials/header.pug 
        //- thằng này dùng cho thằng home với thằng product ấy 
        block main

        include ../partials/footer.pug

        script(src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js")
        script(src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js")  
        script(src="/js/script.js") 