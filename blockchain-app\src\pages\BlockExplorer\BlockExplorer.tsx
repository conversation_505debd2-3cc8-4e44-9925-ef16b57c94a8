import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  IconButton,
} from '@mui/material';
import {
  Search,
  Block,
  Receipt,
  AccountBalance,
  Refresh,
  OpenInNew,
} from '@mui/icons-material';
import { useBlockchain } from '../../hooks/useBlockchain';
import { useWallet } from '../../hooks/useWallet';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const BlockExplorer: React.FC = () => {
  const { networkStats, latestBlocks, loading, getBlockByNumber, searchAddress } = useBlockchain();
  const { walletState } = useWallet();
  
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResult, setSearchResult] = useState<any>(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSearchResult(null);
    setSearchError(null);
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setSearchLoading(true);
    setSearchError(null);
    setSearchResult(null);

    try {
      if (tabValue === 0) {
        // Search for block
        const blockNumber = parseInt(searchQuery);
        if (isNaN(blockNumber)) {
          throw new Error('Please enter a valid block number');
        }
        const block = await getBlockByNumber(blockNumber);
        setSearchResult(block);
      } else if (tabValue === 1) {
        // Search for address
        if (!searchQuery.match(/^0x[a-fA-F0-9]{40}$/)) {
          throw new Error('Please enter a valid Ethereum address');
        }
        const addressInfo = await searchAddress(searchQuery);
        setSearchResult(addressInfo);
      }
    } catch (error: any) {
      setSearchError(error.message);
    } finally {
      setSearchLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const truncateHash = (hash: string, length: number = 12) => {
    if (!hash) return '';
    return `${hash.slice(0, length)}...${hash.slice(-4)}`;
  };

  const openInExplorer = (hash: string, type: 'tx' | 'block' | 'address' = 'tx') => {
    if (walletState.network?.blockExplorerUrl) {
      const url = `${walletState.network.blockExplorerUrl}/${type}/${hash}`;
      window.open(url, '_blank');
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Block Explorer
      </Typography>

      {!walletState.isConnected && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Connect your wallet to access full blockchain explorer features
        </Alert>
      )}

      {/* Network Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Block sx={{ mr: 1, color: 'primary.main' }} />
                <Typography color="textSecondary">Latest Block</Typography>
              </Box>
              <Typography variant="h5">
                {networkStats?.blockHeight?.toLocaleString() || 'N/A'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Receipt sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography color="textSecondary">Gas Price</Typography>
              </Box>
              <Typography variant="h5">
                {networkStats?.gasPrice ? `${parseFloat(networkStats.gasPrice).toFixed(2)} Gwei` : 'N/A'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AccountBalance sx={{ mr: 1, color: 'success.main' }} />
                <Typography color="textSecondary">Network</Typography>
              </Box>
              <Typography variant="h6">
                {walletState.network?.name || 'Not Connected'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label="Search Block" />
            <Tab label="Search Address" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <TextField
                fullWidth
                label="Block Number"
                placeholder="Enter block number (e.g., ********)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                type="number"
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                disabled={searchLoading || !walletState.isConnected}
                startIcon={searchLoading ? <CircularProgress size={20} /> : <Search />}
              >
                Search
              </Button>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <TextField
                fullWidth
                label="Ethereum Address"
                placeholder="Enter address (0x...)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                disabled={searchLoading || !walletState.isConnected}
                startIcon={searchLoading ? <CircularProgress size={20} /> : <Search />}
              >
                Search
              </Button>
            </Box>
          </TabPanel>

          {searchError && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {searchError}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResult && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search Results
            </Typography>
            
            {tabValue === 0 && searchResult && (
              // Block Details
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Block Number</Typography>
                  <Typography variant="body1" fontFamily="monospace">{searchResult.number}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Block Hash</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1" fontFamily="monospace" sx={{ mr: 1 }}>
                      {truncateHash(searchResult.hash)}
                    </Typography>
                    <IconButton size="small" onClick={() => openInExplorer(searchResult.hash, 'block')}>
                      <OpenInNew fontSize="small" />
                    </IconButton>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Timestamp</Typography>
                  <Typography variant="body1">{formatTimestamp(searchResult.timestamp)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Transactions</Typography>
                  <Typography variant="body1">{searchResult.transactionCount}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Gas Used</Typography>
                  <Typography variant="body1">{parseInt(searchResult.gasUsed || '0').toLocaleString()}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Gas Limit</Typography>
                  <Typography variant="body1">{parseInt(searchResult.gasLimit || '0').toLocaleString()}</Typography>
                </Grid>
              </Grid>
            )}

            {tabValue === 1 && searchResult && (
              // Address Details
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">Address</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body1" fontFamily="monospace" sx={{ mr: 1 }}>
                      {searchQuery}
                    </Typography>
                    <IconButton size="small" onClick={() => openInExplorer(searchQuery, 'address')}>
                      <OpenInNew fontSize="small" />
                    </IconButton>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Balance</Typography>
                  <Typography variant="h6">
                    {searchResult.balance} {walletState.network?.nativeCurrency?.symbol || 'ETH'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Transaction Count</Typography>
                  <Typography variant="h6">{searchResult.transactionCount}</Typography>
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>
      )}

      {/* Latest Blocks */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Latest Blocks</Typography>
            <IconButton onClick={() => window.location.reload()} disabled={loading}>
              <Refresh />
            </IconButton>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : latestBlocks.length === 0 ? (
            <Typography color="textSecondary" sx={{ py: 4, textAlign: 'center' }}>
              {walletState.isConnected ? 'No blocks data available' : 'Connect wallet to view latest blocks'}
            </Typography>
          ) : (
            <TableContainer component={Paper} sx={{ backgroundColor: 'transparent' }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Block</TableCell>
                    <TableCell>Hash</TableCell>
                    <TableCell>Transactions</TableCell>
                    <TableCell>Gas Used</TableCell>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {latestBlocks.map((block, index) => (
                    <TableRow key={block.hash || index} hover>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {block.number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {truncateHash(block.hash)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip label={block.transactionCount} size="small" />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {parseInt(block.gasUsed || '0').toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatTimestamp(block.timestamp)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => openInExplorer(block.hash, 'block')}
                          disabled={!walletState.network?.blockExplorerUrl}
                        >
                          <OpenInNew fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default BlockExplorer;
