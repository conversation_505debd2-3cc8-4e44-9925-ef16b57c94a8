/* header */
.header {
  background: #202020;
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 999;
}

.header .inner-logo a {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
}
/* end header */

/* body */

.body .sider {
  width: 150px;
  background-color: #323232;
  color: #fff;
  height: calc(100vh - 56px);
  position: fixed;
  top: 56px;
  left: 0;
}
.body .main {
  margin: 15px;
  position: relative;
  left: 150px;
  width: calc(100% - 180px);
}

.sider .inner-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.sider .inner-menu ul li a {
  color: #fff;
  display: block;
  padding: 6px 10px;
  border-bottom: 1px solid #515151;
}

/* show alert */
[show-alert] {
  position: fixed;
  top: 70px;
  right: 15px;
  z-index: 9999;
}

[show-alert].alert-hidden {
  animation-name: alert-hidden;
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

@keyframes alert-hidden {
  from {
    right: 15px;
  }
  to {
    right: -100%;
    display: none;
  }
}

[show-alert] [close-alert] {
  background: #ffffffc4;
  display: inline-block;
  width: 26px;
  height: 27px;
  line-height: 24px;
  text-align: center;
  font-weight: 600;
  border-radius: 50%;
  cursor: pointer;
}
/* end show alert */
/* uploadImage */

.image-preview {
  width: 150px;
  height: auto;
  margin-top: 15px;
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 5px;
}
.image-preview[src=""] {
  display: none;
}

/* end uploadImage */
.detail {
  border: 1px solid black;
  padding: 10px;
  border-radius: 5px;
  object-fit: cover; /* đéo hiểu */
  display: flex;
  justify-content: center;
  align-items: center;
}
