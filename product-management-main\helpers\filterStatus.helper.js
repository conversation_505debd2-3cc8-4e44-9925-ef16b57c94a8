module.exports = (hehe) => {
  let filterStatus = [
    {
      name: "<PERSON><PERSON><PERSON>ả",
      status: "",
      class: "",
    },
    {
      name: "<PERSON>ạ<PERSON> Động",
      status: "active",
      class: "",
    },
    {
      name: "<PERSON><PERSON>ng Hoạt Động",
      status: "inactive",
      class: "",
    },
  ];
  //hehe = req.query (product-controller)
  if (hehe.status) {
    const index = filterStatus.findIndex((item) => item.status == hehe.status);
    filterStatus[index].class = "active";
  } else {
    const index = filterStatus.findIndex((item) => item.status == "");
    filterStatus[index].class = "active";
  }

  return filterStatus;
};
