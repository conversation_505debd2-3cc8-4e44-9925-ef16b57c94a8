extends ../../layouts/default.pug
include ../../mixins/alert.pug


block main 
    h1 Chỉnh sửa sản phẩm
    .card.mb-3  
        .card-header Change products !!   
        .card-body
            +alert-success(5000)
            +alert-error(5000) 
            form(
                action=`${prefixAdmin}/products/edit/${product.id}?_method=PATCH`
                id="form-edit-product"
                method="POST"
                enctype="multipart/form-data"
            )
                div(class="form-group ")
                    label(for="title") Tiêu đ<PERSON>
                    input(
                        type="text"
                        class="form-control "
                        id="title"
                        name="title"
                        required 
                        value=product.title
                    )
                div(class="form-group")
                    label(for="desc") Mô Tả
                    textarea(
                        class="form-control"
                        id="desc"
                        name="description"
                        rows="5"
                        value=product.description
                    ) #{product.description}
                div(class="form-group")
                    label(for="price") Giá
                    input(
                        type="number"
                        class="form-control"
                        id="price"
                        name="price"
                        value=product.price
                        min="0"
                    )
                div(class="form-group")
                    label(for="discount") %Giảm Giá
                    input(
                        type="number"
                        class="form-control"
                        id="discount"
                        name="discountPercentage"
                        min="0"
                        value=product.discountPercentage
                    )
                div(class="form-group")
                    label(for="stock") số lượng
                    input(
                        type="number"
                        class="form-control"
                        id="stock"
                        name="stock"
                        min="0"
                        value=product.stock

                    )
                div(
                    class="form-group"
                    upload-image
                )
                    label(for="thumbnail") Ảnh
                    input(
                        type="file"
                        class="form-control-file"
                        id="thumbnail"
                        name="thumbnail"
                        accept="image/*"
                        upload-image-input
                    )
                    img(
                        src=product.thumbnail
                        upload-image-preview
                        class="image-preview"
                    )
                div(class="form-group")
                    label(for="position") vị trí
                    input(
                        type="number"
                        class="form-control"
                        id="position"
                        name="position"
                        min="1"
                        value= product.position
                    )
                div(class="form-group form-check form-check-inline")
                    input(
                        type="radio"
                        class="form-check-input"
                        id="statusActive"
                        name="status"
                        value="active"
                        checked=(product.status == "active" ? true : false)
                    )
                    label(for="statusActive" class="form-check-label") Hoạt Động
                div(class="form-group form-check form-check-inline")
                    input(
                        type="radio"
                        class="form-check-input"
                        id="statusActive"
                        name="status"
                        value="inactive"
                        checked=(product.status == "inactive" ? true : false)
                    )
                    label(for="statusActive" class="form-check-label") Dừng Hoạt Động
                div(class="form-group")
                    
                button(
                    type="submit"
                    class="btn btn-outline-primary"
                ) Save !
    .card-footer
        div(
            class="text-center"
        ) Contact me !!!!
        
        