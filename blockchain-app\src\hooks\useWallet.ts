import { useState, useEffect, useCallback } from 'react';
import { web3Service, NetworkConfig } from '../services/web3Service';

export interface WalletState {
  address: string | null;
  balance: string;
  network: NetworkConfig | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

export const useWallet = () => {
  const [walletState, setWalletState] = useState<WalletState>({
    address: null,
    balance: '0',
    network: null,
    isConnected: false,
    isConnecting: false,
    error: null,
  });

  const [transactions, setTransactions] = useState<any[]>([]);
  const [loadingTransactions, setLoadingTransactions] = useState(false);

  // Check if wallet is already connected on mount
  useEffect(() => {
    checkConnection();
    setupEventListeners();
    
    return () => {
      removeEventListeners();
    };
  }, []);

  const checkConnection = async () => {
    try {
      if (window.ethereum && web3Service.isConnected()) {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
        if (accounts.length > 0) {
          await connectWallet();
        }
      }
    } catch (error) {
      console.error('Error checking connection:', error);
    }
  };

  const setupEventListeners = () => {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      window.ethereum.on('chainChanged', handleChainChanged);
      window.ethereum.on('disconnect', handleDisconnect);
    }
  };

  const removeEventListeners = () => {
    if (window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
      window.ethereum.removeListener('chainChanged', handleChainChanged);
      window.ethereum.removeListener('disconnect', handleDisconnect);
    }
  };

  const handleAccountsChanged = (accounts: string[]) => {
    if (accounts.length === 0) {
      disconnect();
    } else {
      // Account changed, reconnect
      connectWallet();
    }
  };

  const handleChainChanged = () => {
    // Chain changed, reconnect to get new network info
    if (walletState.isConnected) {
      connectWallet();
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  const connectWallet = useCallback(async () => {
    setWalletState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const walletInfo = await web3Service.connectWallet();
      
      setWalletState({
        address: walletInfo.address,
        balance: walletInfo.balance,
        network: walletInfo.network,
        isConnected: true,
        isConnecting: false,
        error: null,
      });

      // Load transaction history
      loadTransactionHistory(walletInfo.address);
      
      return walletInfo;
    } catch (error: any) {
      setWalletState(prev => ({
        ...prev,
        isConnecting: false,
        error: error.message,
      }));
      throw error;
    }
  }, []);

  const disconnect = useCallback(() => {
    web3Service.disconnect();
    setWalletState({
      address: null,
      balance: '0',
      network: null,
      isConnected: false,
      isConnecting: false,
      error: null,
    });
    setTransactions([]);
  }, []);

  const refreshBalance = useCallback(async () => {
    if (!walletState.address) return;

    try {
      const balance = await web3Service.getBalance(walletState.address);
      setWalletState(prev => ({ ...prev, balance }));
    } catch (error: any) {
      setWalletState(prev => ({ ...prev, error: error.message }));
    }
  }, [walletState.address]);

  const sendTransaction = useCallback(async (to: string, amount: string, gasPrice?: string) => {
    if (!walletState.isConnected) {
      throw new Error('Wallet not connected');
    }

    try {
      const txHash = await web3Service.sendTransaction(to, amount, gasPrice);
      
      // Refresh balance and transaction history after sending
      setTimeout(() => {
        refreshBalance();
        if (walletState.address) {
          loadTransactionHistory(walletState.address);
        }
      }, 2000); // Wait a bit for transaction to be mined

      return txHash;
    } catch (error: any) {
      throw new Error(error.message || 'Transaction failed');
    }
  }, [walletState.isConnected, walletState.address, refreshBalance]);

  const loadTransactionHistory = useCallback(async (address: string, limit: number = 10) => {
    setLoadingTransactions(true);
    try {
      const txHistory = await web3Service.getTransactionHistory(address, limit);
      setTransactions(txHistory);
    } catch (error) {
      console.error('Error loading transaction history:', error);
      setTransactions([]);
    } finally {
      setLoadingTransactions(false);
    }
  }, []);

  const switchNetwork = useCallback(async (chainId: string) => {
    try {
      await web3Service.switchNetwork(chainId);
      // Reconnect to get updated network info
      if (walletState.isConnected) {
        await connectWallet();
      }
    } catch (error: any) {
      setWalletState(prev => ({ ...prev, error: error.message }));
      throw error;
    }
  }, [walletState.isConnected, connectWallet]);

  const clearError = useCallback(() => {
    setWalletState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    walletState,
    transactions,
    loadingTransactions,
    connectWallet,
    disconnect,
    refreshBalance,
    sendTransaction,
    loadTransactionHistory,
    switchNetwork,
    clearError,
  };
};
