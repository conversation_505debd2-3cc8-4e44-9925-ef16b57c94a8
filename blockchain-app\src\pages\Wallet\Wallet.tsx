import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
} from "@mui/material";
import {
  AccountBalanceWallet,
  ContentCopy,
  Send,
  CallReceived,
  Refresh,
  SwapHoriz,
} from "@mui/icons-material";
import { mockWalletData, mockTransactions } from "../../data/mockData";
import { NETWORKS } from "../../services/web3Service";

const Wallet: React.FC = () => {
  // Sử dụng mock data thay vì hook thật
  const walletState = { ...mockWalletData, isConnected: true };
  const transactions = mockTransactions;

  // Dialog states
  const [sendDialogOpen, setSendDialogOpen] = useState(false);
  const [receiveDialogO<PERSON>, setReceiveDialogOpen] = useState(false);
  const [networkDialogOpen, setNetworkDialogOpen] = useState(false);

  // Send transaction form
  const [sendForm, setSendForm] = useState({
    to: "",
    amount: "",
    gasPrice: "20",
  });

  // Loading states
  const [sendingTransaction, setSendingTransaction] = useState(false);
  const [switchingNetwork, setSwitchingNetwork] = useState(false);

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success" as "success" | "error",
  });

  const showSnackbar = (
    message: string,
    severity: "success" | "error" = "success"
  ) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleConnectWallet = async () => {
    try {
      await connectWallet();
      showSnackbar("Wallet connected successfully!");
    } catch (error: any) {
      showSnackbar(error.message, "error");
    }
  };

  const handleDisconnect = () => {
    disconnect();
    showSnackbar("Wallet disconnected");
  };

  const handleRefreshBalance = async () => {
    try {
      await refreshBalance();
      showSnackbar("Balance refreshed");
    } catch (error: any) {
      showSnackbar(error.message, "error");
    }
  };

  const handleSendTransaction = async () => {
    if (!sendForm.to || !sendForm.amount) {
      showSnackbar("Please fill in all required fields", "error");
      return;
    }

    setSendingTransaction(true);
    try {
      const txHash = await sendTransaction(
        sendForm.to,
        sendForm.amount,
        sendForm.gasPrice
      );
      showSnackbar(`Transaction sent! Hash: ${txHash.slice(0, 10)}...`);
      setSendDialogOpen(false);
      setSendForm({ to: "", amount: "", gasPrice: "20" });
    } catch (error: any) {
      showSnackbar(error.message, "error");
    } finally {
      setSendingTransaction(false);
    }
  };

  const handleSwitchNetwork = async (chainId: string) => {
    setSwitchingNetwork(true);
    try {
      await switchNetwork(chainId);
      showSnackbar("Network switched successfully!");
      setNetworkDialogOpen(false);
    } catch (error: any) {
      showSnackbar(error.message, "error");
    } finally {
      setSwitchingNetwork(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showSnackbar("Copied to clipboard!");
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4" component="h1">
          Wallet Management
        </Typography>
        {walletState.isConnected && (
          <IconButton onClick={handleRefreshBalance}>
            <Refresh />
          </IconButton>
        )}
      </Box>

      {walletState.error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={clearError}>
          {walletState.error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Wallet Connection Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <AccountBalanceWallet sx={{ mr: 1 }} />
                <Typography variant="h6">Wallet Connection</Typography>
              </Box>

              {!walletState.isConnected ? (
                <Box>
                  <Typography color="textSecondary" sx={{ mb: 2 }}>
                    Connect your MetaMask wallet to get started
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={handleConnectWallet}
                    disabled={walletState.isConnecting}
                    startIcon={
                      walletState.isConnecting ? (
                        <CircularProgress size={20} />
                      ) : (
                        <AccountBalanceWallet />
                      )
                    }
                  >
                    {walletState.isConnecting
                      ? "Connecting..."
                      : "Connect Wallet"}
                  </Button>
                </Box>
              ) : (
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Address
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <Typography
                      variant="body1"
                      fontFamily="monospace"
                      sx={{ mr: 1 }}
                    >
                      {truncateAddress(walletState.address!)}
                    </Typography>
                    <Tooltip title="Copy address">
                      <IconButton
                        size="small"
                        onClick={() => copyToClipboard(walletState.address!)}
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <Typography variant="body2" color="textSecondary">
                    Balance
                  </Typography>
                  <Typography variant="h5" sx={{ mb: 2 }}>
                    {walletState.balance}{" "}
                    {walletState.network?.nativeCurrency?.symbol || "ETH"}
                  </Typography>

                  <Typography variant="body2" color="textSecondary">
                    Network
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <Chip
                      label={walletState.network?.name || "Unknown"}
                      color="primary"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Button
                      size="small"
                      startIcon={<SwapHoriz />}
                      onClick={() => setNetworkDialogOpen(true)}
                    >
                      Switch
                    </Button>
                  </Box>

                  <Button
                    variant="outlined"
                    color="error"
                    onClick={handleDisconnect}
                    size="small"
                  >
                    Disconnect
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Actions Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Send />}
                    onClick={() => setSendDialogOpen(true)}
                    disabled={!walletState.isConnected}
                  >
                    Send
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<CallReceived />}
                    onClick={() => setReceiveDialogOpen(true)}
                    disabled={!walletState.isConnected}
                  >
                    Receive
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Send Transaction Dialog */}
      <Dialog
        open={sendDialogOpen}
        onClose={() => setSendDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Send Transaction</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Recipient Address"
            fullWidth
            variant="outlined"
            value={sendForm.to}
            onChange={(e) => setSendForm({ ...sendForm, to: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Amount"
            type="number"
            fullWidth
            variant="outlined"
            value={sendForm.amount}
            onChange={(e) =>
              setSendForm({ ...sendForm, amount: e.target.value })
            }
            InputProps={{
              endAdornment:
                walletState.network?.nativeCurrency?.symbol || "ETH",
            }}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Gas Price (Gwei)"
            type="number"
            fullWidth
            variant="outlined"
            value={sendForm.gasPrice}
            onChange={(e) =>
              setSendForm({ ...sendForm, gasPrice: e.target.value })
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSendTransaction}
            variant="contained"
            disabled={sendingTransaction}
            startIcon={
              sendingTransaction ? <CircularProgress size={20} /> : <Send />
            }
          >
            {sendingTransaction ? "Sending..." : "Send"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Receive Dialog */}
      <Dialog
        open={receiveDialogOpen}
        onClose={() => setReceiveDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Receive Funds</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Share this address to receive funds:
          </Typography>
          <Box
            sx={{
              p: 2,
              bgcolor: "background.paper",
              border: 1,
              borderColor: "divider",
              borderRadius: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Typography variant="body1" fontFamily="monospace">
              {walletState.address}
            </Typography>
            <IconButton onClick={() => copyToClipboard(walletState.address!)}>
              <ContentCopy />
            </IconButton>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReceiveDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Network Switch Dialog */}
      <Dialog
        open={networkDialogOpen}
        onClose={() => setNetworkDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Switch Network</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 1 }}>
            <InputLabel>Select Network</InputLabel>
            <Select
              value=""
              label="Select Network"
              onChange={(e) => handleSwitchNetwork(e.target.value)}
              disabled={switchingNetwork}
            >
              {Object.entries(NETWORKS).map(([chainId, network]) => (
                <MenuItem key={chainId} value={chainId}>
                  {network.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNetworkDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Wallet;
