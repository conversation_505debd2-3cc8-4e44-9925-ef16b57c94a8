import Web3 from 'web3';
import { ethers } from 'ethers';

export interface NetworkConfig {
  chainId: string;
  name: string;
  rpcUrl: string;
  blockExplorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

export const NETWORKS: { [key: string]: NetworkConfig } = {
  '0x1': {
    chainId: '0x1',
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
    blockExplorerUrl: 'https://etherscan.io',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 }
  },
  '0x5': {
    chainId: '0x5',
    name: 'Go<PERSON><PERSON> Testnet',
    rpcUrl: 'https://goerli.infura.io/v3/YOUR_INFURA_KEY',
    blockExplorerUrl: 'https://goerli.etherscan.io',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'GoerliETH', decimals: 18 }
  },
  '0xaa36a7': {
    chainId: '0xaa36a7',
    name: '<PERSON><PERSON> Testnet',
    rpcUrl: 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY',
    blockExplorerUrl: 'https://sepolia.etherscan.io',
    nativeCurrency: { name: 'Sepolia Ether', symbol: 'SepoliaETH', decimals: 18 }
  },
  '0x89': {
    chainId: '0x89',
    name: 'Polygon Mainnet',
    rpcUrl: 'https://polygon-rpc.com',
    blockExplorerUrl: 'https://polygonscan.com',
    nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 }
  }
};

class Web3Service {
  private web3: Web3 | null = null;
  private provider: ethers.BrowserProvider | null = null;
  private signer: ethers.JsonRpcSigner | null = null;

  async connectWallet(): Promise<{
    address: string;
    balance: string;
    network: NetworkConfig;
  }> {
    if (!window.ethereum) {
      throw new Error('MetaMask is not installed. Please install MetaMask to continue.');
    }

    try {
      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts',
      });

      if (accounts.length === 0) {
        throw new Error('No accounts found. Please connect your wallet.');
      }

      // Initialize providers
      this.web3 = new Web3(window.ethereum);
      this.provider = new ethers.BrowserProvider(window.ethereum);
      this.signer = await this.provider.getSigner();

      const address = accounts[0];
      
      // Get balance
      const balanceWei = await this.web3.eth.getBalance(address);
      const balance = this.web3.utils.fromWei(balanceWei, 'ether');

      // Get network info
      const chainId = await window.ethereum.request({ method: 'eth_chainId' });
      const network = NETWORKS[chainId] || {
        chainId,
        name: `Unknown Network (${chainId})`,
        rpcUrl: '',
        blockExplorerUrl: '',
        nativeCurrency: { name: 'Unknown', symbol: 'UNKNOWN', decimals: 18 }
      };

      return {
        address,
        balance: parseFloat(balance).toFixed(4),
        network
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to connect wallet');
    }
  }

  async getBalance(address: string): Promise<string> {
    if (!this.web3) {
      throw new Error('Web3 not initialized');
    }

    const balanceWei = await this.web3.eth.getBalance(address);
    const balance = this.web3.utils.fromWei(balanceWei, 'ether');
    return parseFloat(balance).toFixed(4);
  }

  async sendTransaction(to: string, amount: string, gasPrice?: string): Promise<string> {
    if (!this.signer) {
      throw new Error('Wallet not connected');
    }

    try {
      const tx = {
        to,
        value: ethers.parseEther(amount),
        gasPrice: gasPrice ? ethers.parseUnits(gasPrice, 'gwei') : undefined,
      };

      const transaction = await this.signer.sendTransaction(tx);
      return transaction.hash;
    } catch (error: any) {
      throw new Error(error.message || 'Transaction failed');
    }
  }

  async getTransactionHistory(address: string, limit: number = 10): Promise<any[]> {
    if (!this.web3) {
      throw new Error('Web3 not initialized');
    }

    try {
      // Get latest block number
      const latestBlock = await this.web3.eth.getBlockNumber();
      const transactions: any[] = [];

      // Search through recent blocks for transactions involving this address
      const blocksToSearch = Math.min(100, Number(latestBlock));
      
      for (let i = 0; i < blocksToSearch && transactions.length < limit; i++) {
        const blockNumber = Number(latestBlock) - i;
        const block = await this.web3.eth.getBlock(blockNumber, true);
        
        if (block && block.transactions) {
          for (const tx of block.transactions) {
            if (typeof tx === 'object' && (tx.from === address || tx.to === address)) {
              const receipt = await this.web3.eth.getTransactionReceipt(tx.hash);
              
              transactions.push({
                hash: tx.hash,
                from: tx.from,
                to: tx.to || 'Contract Creation',
                value: this.web3.utils.fromWei(tx.value.toString(), 'ether'),
                gasUsed: receipt?.gasUsed?.toString() || '0',
                gasPrice: tx.gasPrice?.toString() || '0',
                status: receipt?.status ? 'success' : 'failed',
                blockNumber: tx.blockNumber,
                timestamp: new Date(Number(block.timestamp) * 1000).toISOString(),
                type: tx.from === address ? 'sent' : 'received'
              });

              if (transactions.length >= limit) break;
            }
          }
        }
      }

      return transactions.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      return [];
    }
  }

  async getBlockInfo(blockNumber?: number): Promise<any> {
    if (!this.web3) {
      throw new Error('Web3 not initialized');
    }

    const block = await this.web3.eth.getBlock(blockNumber || 'latest', true);
    return {
      number: block?.number?.toString(),
      hash: block?.hash,
      parentHash: block?.parentHash,
      timestamp: block?.timestamp ? new Date(Number(block.timestamp) * 1000).toISOString() : null,
      gasLimit: block?.gasLimit?.toString(),
      gasUsed: block?.gasUsed?.toString(),
      transactionCount: block?.transactions?.length || 0,
      miner: block?.miner,
      difficulty: block?.difficulty?.toString(),
      size: block?.size?.toString()
    };
  }

  async getNetworkStats(): Promise<{
    blockHeight: number;
    gasPrice: string;
    networkHashRate?: string;
  }> {
    if (!this.web3) {
      throw new Error('Web3 not initialized');
    }

    const [blockNumber, gasPrice] = await Promise.all([
      this.web3.eth.getBlockNumber(),
      this.web3.eth.getGasPrice()
    ]);

    return {
      blockHeight: Number(blockNumber),
      gasPrice: this.web3.utils.fromWei(gasPrice.toString(), 'gwei'),
    };
  }

  async switchNetwork(chainId: string): Promise<void> {
    if (!window.ethereum) {
      throw new Error('MetaMask is not installed');
    }

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId }],
      });
    } catch (switchError: any) {
      // This error code indicates that the chain has not been added to MetaMask
      if (switchError.code === 4902) {
        const network = NETWORKS[chainId];
        if (network) {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId,
                chainName: network.name,
                rpcUrls: [network.rpcUrl],
                blockExplorerUrls: [network.blockExplorerUrl],
                nativeCurrency: network.nativeCurrency,
              },
            ],
          });
        }
      } else {
        throw switchError;
      }
    }
  }

  disconnect(): void {
    this.web3 = null;
    this.provider = null;
    this.signer = null;
  }

  isConnected(): boolean {
    return this.web3 !== null && this.provider !== null;
  }
}

export const web3Service = new Web3Service();
