{"name": "product-management", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon --inspect index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^1.20.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.7", "dotenv": "^16.4.5", "express": "^4.21.0", "express-flash": "^0.0.2", "express-session": "^1.18.1", "method-override": "^3.0.0", "mongodb": "^6.12.0", "mongoose": "^8.7.0", "mongoose-slug-updater": "^3.3.0", "multer": "^1.4.5-lts.1", "pug": "^3.0.3", "streamifier": "^0.1.1"}, "devDependencies": {"nodemon": "^3.1.10"}}