mixin pagination(pagination)
    nav 
        ul(class="pagination")
            //- div #{pagination.currentPage}
            if(pagination.currentPage < 4)
                li(class="page-item")
                    button(
                        class="page-link"
                        button-paginationHEHE = pagination.totalPage
                    ) last page
            if(pagination.currentPage > 1)
                li(class="page-item")
                    button(
                        class="page-link"
                        button-paginationHEHE = pagination.currentPage - 1
                    ) Trang Trước
            - for(var i = 1; i <= pagination.totalPage; i++ )
                li(class=`page-item ${pagination.currentPage == i ? "active" : ""}`)
                    button(
                        class="page-link"
                        button-paginationHEHE = i
                    ) #{i}
            if(pagination.currentPage < pagination.totalPage)
                li(class="page-item")
                    button(
                        class="page-link"
                        button-paginationHEHE = pagination.currentPage + 1
                    ) <PERSON>ế Tiếp
            if(pagination.currentPage > pagination.totalPage - 1 )
                li(class="page-item")
                    button(
                        class="page-link"
                        button-paginationHEHE = 1
                    )  first page                       
