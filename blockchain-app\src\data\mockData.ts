// Mock data cho demo ứng dụng blockchain
export const mockWalletData = {
  isConnected: true,
  address: "******************************************",
  balance: "2.5847",
  network: "Ethereum Mainnet",
  networkId: 1,
};

export const mockTransactions = [
  {
    id: "1",
    hash: "******************************************",
    from: "******************************************",
    to: "0x8ba1f109551bD432803012645Hac136c22C4C4e4",
    value: "0.5",
    status: "success",
    timestamp: Date.now() - 3600000, // 1 hour ago
    gasUsed: "21000",
    gasPrice: "20",
    type: "send"
  },
  {
    id: "2", 
    hash: "******************************************",
    from: "0x8ba1f109551bD432803012645Hac136c22C4C4e4",
    to: "******************************************",
    value: "1.2",
    status: "success",
    timestamp: Date.now() - 7200000, // 2 hours ago
    gasUsed: "21000",
    gasPrice: "18",
    type: "receive"
  },
  {
    id: "3",
    hash: "******************************************",
    from: "******************************************",
    to: "0x9cd2f109551bD432803012645Hac136c22C4C4e4",
    value: "0.8",
    status: "pending",
    timestamp: Date.now() - 1800000, // 30 minutes ago
    gasUsed: "21000",
    gasPrice: "25",
    type: "send"
  },
  {
    id: "4",
    hash: "0xcdef1234567890abcdef1234567890abcdef1234",
    from: "******************************************",
    to: "0x1ef4f109551bD432803012645Hac136c22C4C4e4",
    value: "2.1",
    status: "failed",
    timestamp: Date.now() - 10800000, // 3 hours ago
    gasUsed: "0",
    gasPrice: "22",
    type: "send"
  }
];

export const mockBlockData = [
  {
    number: 18500123,
    hash: "0x1a2b3c4d5e6f7890abcdef1234567890abcdef12",
    timestamp: Date.now() - 900000, // 15 minutes ago
    transactions: 245,
    gasUsed: "12500000",
    gasLimit: "15000000",
    miner: "******************************************"
  },
  {
    number: 18500122,
    hash: "0x2b3c4d5e6f7890abcdef1234567890abcdef1234",
    timestamp: Date.now() - 1800000, // 30 minutes ago
    transactions: 189,
    gasUsed: "11200000",
    gasLimit: "15000000",
    miner: "0x8ba1f109551bD432803012645Hac136c22C4C4e4"
  },
  {
    number: 18500121,
    hash: "******************************************",
    timestamp: Date.now() - 2700000, // 45 minutes ago
    transactions: 312,
    gasUsed: "13800000",
    gasLimit: "15000000",
    miner: "0x9cd2f109551bD432803012645Hac136c22C4C4e4"
  }
];

export const mockNetworkStats = {
  blockHeight: 18500123,
  gasPrice: "22 Gwei",
  networkHashRate: "850 TH/s",
  difficulty: "58.5T",
  totalSupply: "120,373,456 ETH",
  marketCap: "$289.5B",
  price: "$2,405.67"
};

export const mockSmartContracts = [
  {
    name: "USDT Token",
    address: "******************************************",
    type: "ERC-20",
    verified: true,
    transactions: "1,234,567",
    balance: "1000.50 USDT"
  },
  {
    name: "Uniswap V3",
    address: "******************************************",
    type: "DEX",
    verified: true,
    transactions: "987,654",
    balance: "0 ETH"
  },
  {
    name: "Custom Contract",
    address: "******************************************",
    type: "Custom",
    verified: false,
    transactions: "123",
    balance: "5.25 ETH"
  }
];

export const mockDashboardStats = {
  totalBalance: "2.5847 ETH",
  totalTransactions: 156,
  pendingTransactions: 2,
  networkStatus: "Healthy",
  lastBlockTime: "12 seconds ago",
  avgGasPrice: "22 Gwei"
};
