import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Paper,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  ExpandMore,
  Code,
  PlayArrow,
  Visibility,
  Send,
  Description,
} from '@mui/icons-material';
import { useWallet } from '../../hooks/useWallet';

interface ContractMethod {
  name: string;
  type: 'read' | 'write';
  inputs: Array<{
    name: string;
    type: string;
    placeholder?: string;
  }>;
  outputs?: Array<{
    name: string;
    type: string;
  }>;
}

// Common ERC-20 contract methods
const ERC20_METHODS: ContractMethod[] = [
  {
    name: 'balanceOf',
    type: 'read',
    inputs: [{ name: 'account', type: 'address', placeholder: '0x...' }],
    outputs: [{ name: 'balance', type: 'uint256' }]
  },
  {
    name: 'totalSupply',
    type: 'read',
    inputs: [],
    outputs: [{ name: 'supply', type: 'uint256' }]
  },
  {
    name: 'name',
    type: 'read',
    inputs: [],
    outputs: [{ name: 'name', type: 'string' }]
  },
  {
    name: 'symbol',
    type: 'read',
    inputs: [],
    outputs: [{ name: 'symbol', type: 'string' }]
  },
  {
    name: 'decimals',
    type: 'read',
    inputs: [],
    outputs: [{ name: 'decimals', type: 'uint8' }]
  },
  {
    name: 'transfer',
    type: 'write',
    inputs: [
      { name: 'to', type: 'address', placeholder: '0x...' },
      { name: 'amount', type: 'uint256', placeholder: '1000000000000000000' }
    ]
  },
  {
    name: 'approve',
    type: 'write',
    inputs: [
      { name: 'spender', type: 'address', placeholder: '0x...' },
      { name: 'amount', type: 'uint256', placeholder: '1000000000000000000' }
    ]
  }
];

const SmartContract: React.FC = () => {
  const { walletState, connectWallet } = useWallet();
  
  const [contractAddress, setContractAddress] = useState('');
  const [contractType, setContractType] = useState('ERC20');
  const [selectedMethod, setSelectedMethod] = useState<ContractMethod | null>(null);
  const [methodInputs, setMethodInputs] = useState<Record<string, string>>({});
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customABI, setCustomABI] = useState('');

  const handleMethodSelect = (method: ContractMethod) => {
    setSelectedMethod(method);
    setMethodInputs({});
    setResult(null);
    setError(null);
  };

  const handleInputChange = (inputName: string, value: string) => {
    setMethodInputs(prev => ({
      ...prev,
      [inputName]: value
    }));
  };

  const executeMethod = async () => {
    if (!selectedMethod || !contractAddress) {
      setError('Please select a method and enter contract address');
      return;
    }

    if (!walletState.isConnected) {
      setError('Please connect your wallet first');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // This is a simplified implementation
      // In a real app, you would use web3.js or ethers.js to interact with contracts
      
      if (selectedMethod.type === 'read') {
        // Simulate read operation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock results based on method name
        switch (selectedMethod.name) {
          case 'balanceOf':
            setResult({ balance: '1000000000000000000' }); // 1 token
            break;
          case 'totalSupply':
            setResult({ supply: '1000000000000000000000000' }); // 1M tokens
            break;
          case 'name':
            setResult({ name: 'Example Token' });
            break;
          case 'symbol':
            setResult({ symbol: 'EXT' });
            break;
          case 'decimals':
            setResult({ decimals: '18' });
            break;
          default:
            setResult({ result: 'Method executed successfully' });
        }
      } else {
        // Simulate write operation
        await new Promise(resolve => setTimeout(resolve, 2000));
        setResult({ 
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
          status: 'success'
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to execute method');
    } finally {
      setLoading(false);
    }
  };

  const formatResult = (result: any) => {
    if (typeof result === 'object') {
      return JSON.stringify(result, null, 2);
    }
    return result.toString();
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Smart Contract Interaction
      </Typography>

      {!walletState.isConnected && (
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={connectWallet}>
              Connect Wallet
            </Button>
          }
        >
          Please connect your wallet to interact with smart contracts
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Contract Setup */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Contract Setup
              </Typography>
              
              <TextField
                fullWidth
                label="Contract Address"
                placeholder="0x..."
                value={contractAddress}
                onChange={(e) => setContractAddress(e.target.value)}
                sx={{ mb: 2 }}
              />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Contract Type</InputLabel>
                <Select
                  value={contractType}
                  label="Contract Type"
                  onChange={(e) => setContractType(e.target.value)}
                >
                  <MenuItem value="ERC20">ERC-20 Token</MenuItem>
                  <MenuItem value="Custom">Custom Contract</MenuItem>
                </Select>
              </FormControl>

              {contractType === 'Custom' && (
                <TextField
                  fullWidth
                  label="Contract ABI"
                  placeholder="Paste contract ABI JSON here..."
                  multiline
                  rows={4}
                  value={customABI}
                  onChange={(e) => setCustomABI(e.target.value)}
                  sx={{ mb: 2 }}
                />
              )}

              <Alert severity="info">
                <Typography variant="body2">
                  This is a demo interface. In production, you would need the actual contract ABI 
                  and proper Web3 integration for real contract interactions.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* Method Selection */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Methods
              </Typography>
              
              {contractType === 'ERC20' && ERC20_METHODS.map((method, index) => (
                <Accordion key={index}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <Typography sx={{ flexGrow: 1 }}>{method.name}</Typography>
                      <Chip 
                        label={method.type} 
                        color={method.type === 'read' ? 'primary' : 'secondary'}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {method.inputs.map((input, inputIndex) => (
                        <TextField
                          key={inputIndex}
                          fullWidth
                          label={`${input.name} (${input.type})`}
                          placeholder={input.placeholder}
                          value={methodInputs[input.name] || ''}
                          onChange={(e) => handleInputChange(input.name, e.target.value)}
                          sx={{ mb: 1 }}
                        />
                      ))}
                      
                      <Button
                        variant="contained"
                        onClick={() => {
                          setSelectedMethod(method);
                          executeMethod();
                        }}
                        disabled={loading || !walletState.isConnected}
                        startIcon={
                          loading ? <CircularProgress size={20} /> : 
                          method.type === 'read' ? <Visibility /> : <Send />
                        }
                        sx={{ mt: 1 }}
                      >
                        {loading ? 'Executing...' : method.type === 'read' ? 'Read' : 'Write'}
                      </Button>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}

              {contractType === 'Custom' && (
                <Alert severity="info">
                  Please provide a valid ABI to see available methods
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Results */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Execution Results
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {result && (
                <Paper sx={{ p: 2, backgroundColor: 'background.default' }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Result:
                  </Typography>
                  <Typography 
                    variant="body1" 
                    component="pre" 
                    sx={{ 
                      fontFamily: 'monospace',
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-all'
                    }}
                  >
                    {formatResult(result)}
                  </Typography>
                </Paper>
              )}

              {!result && !error && (
                <Typography color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                  Execute a contract method to see results here
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Contract Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Contract Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Contract Address
                  </Typography>
                  <Typography variant="body1" fontFamily="monospace">
                    {contractAddress || 'Not set'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Network
                  </Typography>
                  <Typography variant="body1">
                    {walletState.network?.name || 'Not connected'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Contract Type
                  </Typography>
                  <Typography variant="body1">
                    {contractType}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">
                    Connected Account
                  </Typography>
                  <Typography variant="body1" fontFamily="monospace">
                    {walletState.address ? 
                      `${walletState.address.slice(0, 6)}...${walletState.address.slice(-4)}` : 
                      'Not connected'
                    }
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SmartContract;
