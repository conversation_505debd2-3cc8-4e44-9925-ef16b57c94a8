import { useState, useEffect, useCallback } from 'react';
import { web3Service } from '../services/web3Service';

export interface BlockInfo {
  number: string;
  hash: string;
  parentHash: string;
  timestamp: string;
  gasLimit: string;
  gasUsed: string;
  transactionCount: number;
  miner: string;
  difficulty: string;
  size: string;
}

export interface NetworkStats {
  blockHeight: number;
  gasPrice: string;
  networkHashRate?: string;
}

export const useBlockchain = () => {
  const [networkStats, setNetworkStats] = useState<NetworkStats | null>(null);
  const [latestBlocks, setLatestBlocks] = useState<BlockInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Auto-refresh interval (30 seconds)
  const REFRESH_INTERVAL = 30000;

  useEffect(() => {
    if (web3Service.isConnected()) {
      loadNetworkData();
      
      const interval = setInterval(() => {
        loadNetworkData();
      }, REFRESH_INTERVAL);

      return () => clearInterval(interval);
    }
  }, []);

  const loadNetworkData = useCallback(async () => {
    if (!web3Service.isConnected()) return;

    setLoading(true);
    setError(null);

    try {
      // Load network stats and latest blocks in parallel
      const [stats, blocks] = await Promise.all([
        loadNetworkStats(),
        loadLatestBlocks(5)
      ]);

      setNetworkStats(stats);
      setLatestBlocks(blocks);
    } catch (err: any) {
      setError(err.message || 'Failed to load blockchain data');
    } finally {
      setLoading(false);
    }
  }, []);

  const loadNetworkStats = async (): Promise<NetworkStats> => {
    return await web3Service.getNetworkStats();
  };

  const loadLatestBlocks = async (count: number = 5): Promise<BlockInfo[]> => {
    const blocks: BlockInfo[] = [];
    
    try {
      const latestBlockInfo = await web3Service.getBlockInfo();
      const latestBlockNumber = parseInt(latestBlockInfo.number);

      // Load the latest 'count' blocks
      const blockPromises = [];
      for (let i = 0; i < count; i++) {
        blockPromises.push(web3Service.getBlockInfo(latestBlockNumber - i));
      }

      const blockResults = await Promise.all(blockPromises);
      return blockResults.filter(block => block !== null);
    } catch (error) {
      console.error('Error loading latest blocks:', error);
      return [];
    }
  };

  const getBlockByNumber = useCallback(async (blockNumber: number): Promise<BlockInfo | null> => {
    if (!web3Service.isConnected()) {
      throw new Error('Web3 not connected');
    }

    try {
      return await web3Service.getBlockInfo(blockNumber);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch block');
    }
  }, []);

  const searchTransaction = useCallback(async (txHash: string): Promise<any> => {
    if (!web3Service.isConnected()) {
      throw new Error('Web3 not connected');
    }

    try {
      // This would need to be implemented in web3Service
      // For now, return null
      return null;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search transaction');
    }
  }, []);

  const searchAddress = useCallback(async (address: string): Promise<{
    balance: string;
    transactionCount: number;
    transactions: any[];
  }> => {
    if (!web3Service.isConnected()) {
      throw new Error('Web3 not connected');
    }

    try {
      const [balance, transactions] = await Promise.all([
        web3Service.getBalance(address),
        web3Service.getTransactionHistory(address, 20)
      ]);

      return {
        balance,
        transactionCount: transactions.length,
        transactions
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search address');
    }
  }, []);

  const refreshData = useCallback(() => {
    loadNetworkData();
  }, [loadNetworkData]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    networkStats,
    latestBlocks,
    loading,
    error,
    getBlockByNumber,
    searchTransaction,
    searchAddress,
    refreshData,
    clearError,
  };
};
