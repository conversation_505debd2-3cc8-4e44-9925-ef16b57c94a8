extends ../../layouts/default.pug
include ../../mixins/filter-status.pug
include ../../mixins/search.pug
include ../../mixins/pagination.pug
include ../../mixins/form-change-multi.pug
include ../../mixins/alert.pug


block main 
    +alert-success("5000")

    h1 Danh S<PERSON>ch Sản Phẩm

    .card.mb-3 
        .card-header Bộ L<PERSON>c Và Tìm Kiếm
        .card-body 
            .row 
                .col-6
                    +filter-status(filterStatus)
                .col-6 
                    +search(keyword)
                            

    .card.mb-3 
        .card-header Danh Sách Sản Phẩm 
        .card-body 
            +formChangeMulti(`${prefixAdmin}/products/change-multi?_method=PATCH`)
            table(
                class = "table table-hover table-sm "
                checkbox-multi
            )
                thead 
                    tr 
                        th 
                            input(type="checkbox" name="checkAll")
                        th STT
                        th Hình ảnh 
                        th Tiêu đề
                        th Giá
                        th Trạng Th<PERSON>i 
                        th Hành động
                tbody 
                    each item, index in products 
                        tr  
                            td 
                                input(
                                    type="checkbox"
                                    name="checkId"
                                    value=item.id
                                )
                            td #{index+1}
                            td 
                                img(
                                    src=item.thumbnail, 
                                    alt=item.title,
                                    width="100px",
                                    height="auto"
                                )
                                td #{item.title}
                                td #{item.price}$
                                td 
                                    if(item.status == "active")
                                            a(
                                                href="javascript:;" 
                                                data-status=item.status
                                                data-id= item.id
                                                button-change-status
                                                class="badge badge-success"
                                            ) Hoạt động
                                    else
                                            a(
                                                href="javascript:;" 
                                                data-status=item.status
                                                data-id= item.id
                                                button-change-status
                                                class="badge badge-danger"
                                            ) DỪng hoạt động

                                td  
                                    button(
                                        class="btn btn-primary btn-lg"
                                        button-update-deleted
                                        data-id = item.id
                                    ) Update again
                                   
    +pagination(pagination)

    form(
        action=""
        method="POST"
        id="form-change-status"
        data-path=`${prefixAdmin}/products/change-status`
    )

    form(
        action=""
        method="POST"
        id="form-update-id"
        data-path=`${prefixAdmin}/storage/updateDeleted`
    )
