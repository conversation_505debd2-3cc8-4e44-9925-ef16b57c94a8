import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Alert,
  LinearProgress,
  Pagination,
  Grid,
} from '@mui/material';
import {
  Search,
  Refresh,
  FilterList,
  OpenInNew,
} from '@mui/icons-material';
import { useWallet } from '../../hooks/useWallet';

interface TransactionFilter {
  status: string;
  type: string;
  search: string;
}

const TransactionHistory: React.FC = () => {
  const { walletState, transactions, loadingTransactions, loadTransactionHistory, connectWallet } = useWallet();
  
  const [filter, setFilter] = useState<TransactionFilter>({
    status: 'all',
    type: 'all',
    search: '',
  });
  
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [filteredTransactions, setFilteredTransactions] = useState<any[]>([]);

  useEffect(() => {
    applyFilters();
  }, [transactions, filter]);

  const applyFilters = () => {
    let filtered = [...transactions];

    // Filter by status
    if (filter.status !== 'all') {
      filtered = filtered.filter(tx => tx.status === filter.status);
    }

    // Filter by type
    if (filter.type !== 'all') {
      filtered = filtered.filter(tx => tx.type === filter.type);
    }

    // Filter by search term
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(tx => 
        tx.hash.toLowerCase().includes(searchTerm) ||
        tx.from.toLowerCase().includes(searchTerm) ||
        tx.to.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredTransactions(filtered);
    setPage(1); // Reset to first page when filters change
  };

  const handleRefresh = () => {
    if (walletState.address) {
      loadTransactionHistory(walletState.address, 50);
    }
  };

  const handleFilterChange = (key: keyof TransactionFilter, value: string) => {
    setFilter(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilter({
      status: 'all',
      type: 'all',
      search: '',
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const truncateHash = (hash: string, length: number = 12) => {
    if (!hash) return '';
    return `${hash.slice(0, length)}...${hash.slice(-4)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    return type === 'sent' ? 'error' : 'success';
  };

  const openInExplorer = (hash: string) => {
    if (walletState.network?.blockExplorerUrl) {
      const url = `${walletState.network.blockExplorerUrl}/tx/${hash}`;
      window.open(url, '_blank');
    }
  };

  // Pagination
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Transaction History
        </Typography>
        <IconButton onClick={handleRefresh} disabled={loadingTransactions}>
          <Refresh />
        </IconButton>
      </Box>

      {!walletState.isConnected && (
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={connectWallet}>
              Connect Wallet
            </Button>
          }
        >
          Please connect your wallet to view transaction history
        </Alert>
      )}

      {loadingTransactions && <LinearProgress sx={{ mb: 2 }} />}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <FilterList sx={{ mr: 1 }} />
            <Typography variant="h6">Filters</Typography>
          </Box>
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search"
                placeholder="Hash, address..."
                value={filter.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filter.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="success">Success</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filter.type}
                  label="Type"
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="sent">Sent</MenuItem>
                  <MenuItem value="received">Received</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                onClick={clearFilters}
                fullWidth
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Transaction Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Transactions ({filteredTransactions.length})
            </Typography>
            {walletState.network && (
              <Chip 
                label={walletState.network.name} 
                color="primary" 
                size="small" 
              />
            )}
          </Box>

          {filteredTransactions.length === 0 ? (
            <Typography color="textSecondary" sx={{ py: 4, textAlign: 'center' }}>
              {walletState.isConnected ? 'No transactions found' : 'Connect wallet to view transactions'}
            </Typography>
          ) : (
            <>
              <TableContainer component={Paper} sx={{ backgroundColor: 'transparent' }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Hash</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>From/To</TableCell>
                      <TableCell>Value</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Gas Used</TableCell>
                      <TableCell>Time</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paginatedTransactions.map((tx, index) => (
                      <TableRow key={tx.hash || index} hover>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {truncateHash(tx.hash)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={tx.type}
                            color={getTypeColor(tx.type)}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {truncateHash(tx.type === 'sent' ? tx.to : tx.from)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {parseFloat(tx.value).toFixed(4)} {walletState.network?.nativeCurrency?.symbol || 'ETH'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={tx.status}
                            color={getStatusColor(tx.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {parseInt(tx.gasUsed || '0').toLocaleString()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatTimestamp(tx.timestamp)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => openInExplorer(tx.hash)}
                            disabled={!walletState.network?.blockExplorerUrl}
                          >
                            <OpenInNew fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default TransactionHistory;
